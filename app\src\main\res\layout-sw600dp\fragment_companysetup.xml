<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraint"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerConstraintLayout"
            android:layout_width="500dp"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">

            <ScrollView
                android:id="@+id/scrollable"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="30dp"
                android:focusableInTouchMode="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/line1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/linearDefaultHourlyRate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/headerInclude">

                        <TextView
                            android:id="@+id/tvDefaultRate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/inter"
                            android:text="Estimate Hourly Rate"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/infoButton1"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/info" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linearDefaultHourlyRate2"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="2dp"
                        android:background="@drawable/rounded_edittext"
                        android:orientation="horizontal"
                        android:padding="6dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/inter"
                            android:paddingLeft="4dp"
                            android:paddingRight="2dp"
                            android:text="$"
                            android:textColor="#868C98"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/edTxtHourlyRate"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:layout_margin="3dp"
                            android:background="@drawable/rounded_edittext2"
                            android:digits="1234567890."
                            android:fontFamily="@font/inter"
                            android:inputType="number"
                            android:maxLines="1"
                            android:text="30"
                            android:textFontWeight="400"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linearDefaultHourlyRate3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:background="@drawable/rounded_edittext"
                        android:orientation="vertical"
                        android:padding="15dp"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate2">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/inter"
                            android:text="This hourly rate is used for estimations on projects. This number will be multiplied by the number of hours in every project estimate to provide a labor budget. Each individual employee will draw from that budget depending on their set hourly rate and hours worked. This number includes both their take-home pay and their tax contributions. Even though this is used for estimations, you can set the owner and employees' hourly rate to whatever you desire."                            android:textColor="#525866"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

<!--                        <TextView-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:fontFamily="@font/inter"-->
<!--                            android:text="This is the employees hourly rate. It includes both their take home pay and their contribution for taxes. This number will be multiplied by the number of hours in every project estimate to provide a labor budget. Each individual employee will draw from that budget depending on their set hourly rate."-->
<!--                            android:textColor="#525866"-->
<!--                            android:textFontWeight="400"-->
<!--                            android:textSize="14sp" />-->

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center">


                            <CheckBox
                                android:id="@+id/checkBox1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:buttonTint="@color/brand_green"
                                android:checkMarkTint="@color/black"
                                android:fontFamily="@font/inter"
                                android:text="Don't show again"
                                android:textAlignment="center"
                                android:textColor="#525866"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/hide1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginRight="10dp"
                                android:fontFamily="@font/inter"
                                android:text="Hide"
                                android:textColor="#525866"
                                android:textFontWeight="500"
                                android:textSize="14sp" />
                        </RelativeLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linearDefaultProfitOverhead"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate3">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/inter"
                            android:text="Default Profit Overhead"
                            android:textColor="#0A0D14"
                            android:textFontWeight="500"
                            android:textSize="14sp" />

                        <ImageView
                            android:id="@+id/infoButton2"
                            android:layout_width="27dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/info" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linearDefaultProfitOverhead2"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="2dp"
                        android:background="@drawable/rounded_edittext"
                        android:orientation="horizontal"
                        android:padding="6dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead">


                        <EditText
                            android:id="@+id/edTxtProfitOverhead"
                            android:layout_width="0dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:layout_margin="3dp"
                            android:layout_weight="9.2"
                            android:background="@drawable/rounded_edittext2"
                            android:digits="1234567890."
                            android:fontFamily="@font/inter"
                            android:inputType="number"
                            android:maxLines="1"
                            android:text="30"
                            android:textAlignment="textEnd"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="0.8"
                            android:fontFamily="@font/inter"
                            android:paddingLeft="2dp"
                            android:paddingRight="4dp"
                            android:text="%"
                            android:textColor="#868C98"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linearDefaultProfitOverhead3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:background="@drawable/rounded_edittext"
                        android:orientation="vertical"
                        android:padding="15dp"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead2">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/inter"
                            android:text="This captures the company profit as well as the company portion of employee taxes and company income taxes."
                            android:textColor="#525866"
                            android:textFontWeight="400"
                            android:textSize="14sp" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center">


                            <CheckBox
                                android:id="@+id/checkBox2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:buttonTint="@color/brand_green"
                                android:checkMarkTint="@color/black"
                                android:fontFamily="@font/inter"
                                android:text="Don't show again"
                                android:textAlignment="center"
                                android:textColor="#525866"
                                android:textFontWeight="500"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/hide2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginRight="10dp"
                                android:fontFamily="@font/inter"
                                android:text="Hide"
                                android:textColor="#525866"
                                android:textFontWeight="500"
                                android:textSize="14sp" />
                        </RelativeLayout>


                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="15dp"
                        android:background="#E2E4E9" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginTop="15dp"
                        android:fontFamily="@font/inter"
                        android:text="Employees"
                        android:textColor="#0A0D14"
                        android:textFontWeight="500"
                        android:textSize="14sp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/employeeRecylerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:paddingBottom="20dp"
                        app:layoutManager="LinearLayoutManager" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnAddEmployee"
                        android:layout_width="match_parent"
                        android:layout_height="54dp"
                        android:layout_marginTop="15dp"
                        android:paddingVertical="0dp"
                        android:fontFamily="@font/inter"
                        android:paddingHorizontal="48dp"
                        android:text="+ Add Employee"
                        android:textColor="#375DFB"
                        android:textFontWeight="600"
                        android:textSize="16sp"
                        app:backgroundTint="@color/white"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3"
                        app:strokeColor="#375DFB"
                        app:strokeWidth="2dp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="10dp"
                        android:background="#E2E4E9" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnContinue"
                        android:layout_width="match_parent"
                        android:layout_height="54dp"
                        android:layout_marginTop="10dp"
                        android:paddingVertical="0dp"
                        android:fontFamily="@font/inter"
                        android:paddingHorizontal="48dp"
                        android:text="Continue"
                        android:textColor="@color/white"
                        android:textFontWeight="600"
                        android:textSize="16sp"
                        app:backgroundTint="@color/black"
                        app:cornerRadius="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
                </LinearLayout>

            </ScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    


</androidx.constraintlayout.widget.ConstraintLayout>
