<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/constraint"
    android:background="@color/app_bg_color">


    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"

        ></include>

    <ScrollView
        android:id="@+id/scrollable"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusableInTouchMode="true"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_60sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/line1"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/linearDefaultHourlyRate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerInclude"
                >

                <TextView
                    android:id="@+id/tvDefaultRate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Estimate Hourly Rate"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_gravity="center"
                    android:textColor="#0A0D14"
                    />

                <ImageView
                    android:id="@+id/infoButton1"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:src="@drawable/info"
                    android:layout_gravity="center"
                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearDefaultHourlyRate2"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/_2sdp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:padding="6dp"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$"
                    android:layout_gravity="center"
                    android:textColor="#868C98"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:paddingLeft="@dimen/_4sdp"
                    android:paddingRight="@dimen/_2sdp"
                    />

                <EditText
                    android:id="@+id/edTxtHourlyRate"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_margin="@dimen/_3sdp"
                    android:layout_gravity="center"
                    android:inputType="number"
                    android:text="30"
                    android:digits="1234567890."
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:maxLines="1"
                    android:background="@drawable/rounded_edittext2"
                    />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearDefaultHourlyRate3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:padding="15dp"
                android:layout_marginTop="@dimen/_15sdp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate2"
                android:visibility="visible"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="This hourly rate is used for estimations on projects. This number will be multiplied by the number of hours in every project estimate to provide a labor budget. Each individual employee will draw from that budget depending on their set hourly rate and hours worked. This number includes both their take-home pay and their tax contributions. Even though this is used for estimations, you can set the owner and employees' hourly rate to whatever you desire."                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_gravity="center"
                    android:textColor="#525866"
                    />
<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="This is the employees hourly rate. It includes both their take home pay and their contribution for taxes. This number will be multiplied by the number of hours in every project estimate to provide a labor budget. Each individual employee will draw from that budget depending on their set hourly rate."-->
<!--                    android:textSize="14sp"-->
<!--                    android:fontFamily="@font/inter"-->
<!--                    android:textFontWeight="400"-->
<!--                    android:layout_gravity="center"-->
<!--                    android:textColor="#525866"-->
<!--                    />-->

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    >


                    <CheckBox
                        android:id="@+id/checkBox1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#525866"
                        android:textAlignment="center"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:text="Don't show again"
                        android:buttonTint="@color/brand_green"
                        android:checkMarkTint="@color/black"/>

                    <TextView
                        android:id="@+id/hide1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hide"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_marginRight="@dimen/_10sdp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"
                        />
                </RelativeLayout>




            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearDefaultProfitOverhead"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate3"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Default Profit Overhead"
                    android:layout_gravity="center"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:textColor="#0A0D14"
                    />

                <ImageView
                    android:id="@+id/infoButton2"
                    android:layout_width="27dp"
                    android:layout_height="@dimen/_20sdp"
                    android:layout_gravity="center"
                    android:src="@drawable/info" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearDefaultProfitOverhead2"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/_2sdp"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:padding="6dp"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead"
                >



                <EditText
                    android:id="@+id/edTxtProfitOverhead"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_margin="@dimen/_3sdp"
                    android:textAlignment="textEnd"
                    android:layout_gravity="center"
                    android:layout_weight="9.2"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:inputType="number"
                    android:text="30"
                    android:digits="1234567890."
                    android:maxLines="1"
                    android:background="@drawable/rounded_edittext2"
                    />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="%"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:layout_weight="0.8"
                    android:layout_gravity="center"
                    android:textColor="#868C98"
                    android:paddingRight="@dimen/_4sdp"
                    android:paddingLeft="@dimen/_2sdp"
                    />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearDefaultProfitOverhead3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rounded_edittext"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:padding="15dp"
                android:layout_marginTop="@dimen/_15sdp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead2"
                android:visibility="visible"
                >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="400"
                    android:text="This captures the company profit as well as the company portion of employee taxes and company income taxes."
                    android:layout_gravity="center"
                    android:textColor="#525866"
                    />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    >


                    <CheckBox
                        android:id="@+id/checkBox2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#525866"
                        android:textAlignment="center"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:text="Don't show again"
                        android:buttonTint="@color/brand_green"
                        android:checkMarkTint="@color/black"/>

                    <TextView
                        android:id="@+id/hide2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hide"
                        android:textSize="14sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="500"
                        android:layout_marginRight="@dimen/_10sdp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:textColor="#525866"
                        />
                </RelativeLayout>




            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:background="#E2E4E9"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Employees"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#0A0D14"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/employeeRecylerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:paddingBottom="20dp"
                app:layoutManager="LinearLayoutManager" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAddEmployee"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_48sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:textColor="#375DFB"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:text="+ Add Employee"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:background="#E2E4E9"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnContinue"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginTop="@dimen/_10sdp"
                android:paddingHorizontal="@dimen/_48sdp"
                android:layout_marginHorizontal="@dimen/_20sdp"
                android:textColor="@color/white"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/black"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                android:text="Continue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearDefaultProfitOverhead3" />
        </LinearLayout>

    </ScrollView>







</androidx.constraintlayout.widget.ConstraintLayout>
