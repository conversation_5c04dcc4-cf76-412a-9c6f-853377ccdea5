package com.manaknight.app.utils

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import com.google.android.material.bottomsheet.BottomSheetDialog
//import com.manaknight.app.ui.components.isTabletLayout
import Manaknight.R
import androidx.compose.runtime.Composable
import com.manaknight.app.ui.utils.isTabletLayout

/**
 * Helper class for creating responsive bottom sheets that show as dialogs on tablets
 * and bottom sheets on phones
 */
class ResponsiveBottomSheetHelper {

    companion object {
        /**
         * Shows a responsive sheet - bottom sheet on phone, dialog on tablet
         */
        @Composable
        fun <T : ViewBinding> showResponsiveSheet(
            fragment: Fragment,
            bindingInflater: (LayoutInflater, ViewGroup?, Boolean) -> T,
            onBindingReady: (T, Dialog) -> Unit,
            onDismiss: (() -> Unit)? = null
        ): Dialog {
            val context = fragment.requireContext()
            val isTablet = isTabletLayout()
            
            return if (isTablet) {
                showAsDialog(context, bindingInflater, onBindingReady, onDismiss)
            } else {
                showAsBottomSheet(context, bindingInflater, onBindingReady, onDismiss)
            }
        }

        /**
         * Shows content as a custom dialog for tablets
         */
        private fun <T : ViewBinding> showAsDialog(
            context: Context,
            bindingInflater: (LayoutInflater, ViewGroup?, Boolean) -> T,
            onBindingReady: (T, Dialog) -> Unit,
            onDismiss: (() -> Unit)?
        ): Dialog {
            val dialog = Dialog(context, R.style.ResponsiveDialogTheme)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            
            val binding = bindingInflater(LayoutInflater.from(context), null, false)
            
            // Set dialog properties for tablet
            dialog.setContentView(binding.root)
            dialog.window?.apply {
                setLayout(
                    context.resources.getDimensionPixelSize(R.dimen.tablet_dialog_width),
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                setBackgroundDrawableResource(R.drawable.dialog_background)
            }
            
            dialog.setCancelable(true)
            onDismiss?.let { dismissCallback ->
                dialog.setOnDismissListener { dismissCallback() }
            }
            
            onBindingReady(binding, dialog)
            dialog.show()
            
            return dialog
        }

        /**
         * Shows content as bottom sheet for phones
         */
        private fun <T : ViewBinding> showAsBottomSheet(
            context: Context,
            bindingInflater: (LayoutInflater, ViewGroup?, Boolean) -> T,
            onBindingReady: (T, Dialog) -> Unit,
            onDismiss: (() -> Unit)?
        ): Dialog {
            val bottomSheetDialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
            val binding = bindingInflater(LayoutInflater.from(context), null, false)
            
            bottomSheetDialog.setContentView(binding.root)
            bottomSheetDialog.setCancelable(true)
            
            onDismiss?.let { dismissCallback ->
                bottomSheetDialog.setOnDismissListener { dismissCallback() }
            }
            
            onBindingReady(binding, bottomSheetDialog)
            bottomSheetDialog.show()
            
            return bottomSheetDialog
        }

        /**
         * Shows a responsive sheet using layout resource (for backward compatibility)
         */
        @Composable
        fun showResponsiveSheetWithLayout(
            fragment: Fragment,
            layoutRes: Int,
            onViewReady: (View, Dialog) -> Unit,
            onDismiss: (() -> Unit)? = null
        ): Dialog {
            val context = fragment.requireContext()
            val isTablet = isTabletLayout()
            
            return if (isTablet) {
                showLayoutAsDialog(context, layoutRes, onViewReady, onDismiss)
            } else {
                showLayoutAsBottomSheet(context, layoutRes, onViewReady, onDismiss)
            }
        }

        private fun showLayoutAsDialog(
            context: Context,
            layoutRes: Int,
            onViewReady: (View, Dialog) -> Unit,
            onDismiss: (() -> Unit)?
        ): Dialog {
            val dialog = Dialog(context, R.style.ResponsiveDialogTheme)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            
            val view = LayoutInflater.from(context).inflate(layoutRes, null)
            dialog.setContentView(view)
            
            dialog.window?.apply {
                setLayout(
                    context.resources.getDimensionPixelSize(R.dimen.tablet_dialog_width),
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                setBackgroundDrawableResource(R.drawable.dialog_background)
            }
            
            dialog.setCancelable(true)
            onDismiss?.let { dismissCallback ->
                dialog.setOnDismissListener { dismissCallback() }
            }
            
            onViewReady(view, dialog)
            dialog.show()
            
            return dialog
        }

        private fun showLayoutAsBottomSheet(
            context: Context,
            layoutRes: Int,
            onViewReady: (View, Dialog) -> Unit,
            onDismiss: (() -> Unit)?
        ): Dialog {
            val bottomSheetDialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
            val view = LayoutInflater.from(context).inflate(layoutRes, null)
            
            bottomSheetDialog.setContentView(view)
            bottomSheetDialog.setCancelable(true)
            
            onDismiss?.let { dismissCallback ->
                bottomSheetDialog.setOnDismissListener { dismissCallback() }
            }
            
            onViewReady(view, bottomSheetDialog)
            bottomSheetDialog.show()
            
            return bottomSheetDialog
        }
    }
}
