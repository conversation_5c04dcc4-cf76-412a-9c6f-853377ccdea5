package com.manaknight.app.utils

import androidx.fragment.app.Fragment
import Manaknight.databinding.BottomAddEmployeeBinding
import Manaknight.databinding.BottomEditProfileBinding
import Manaknight.databinding.BottomUpdatePasswordBinding
import androidx.compose.runtime.Composable
import com.manaknight.app.extensions.hideSoftKeyboard

/**
 * Example usage of ResponsiveBottomSheetHelper and extension functions
 * This file shows how to migrate existing bottom sheet usage to responsive approach
 */

class ResponsiveBottomSheetUsageExample {

    /**
     * Example 1: Using ViewBinding with extension function
     */
    @Composable
    fun Fragment.showAddEmployeeSheet() {
        showResponsiveSheet(BottomAddEmployeeBinding::inflate) { binding, dialog ->
            binding.apply {
                backButton.setOnClickListener {
                    dialog.dismiss()
                }

                btnSave.setOnClickListener {
                    // Add validation and save logic here
                    // if (edTxtFullName.text.toString().isEmpty()) {
                    //     Toast.makeText(context, "Please enter full name", Toast.LENGTH_SHORT).show()
                    // } else {
                    //     // Save employee
                    //     dialog.dismiss()
                    // }
                }
            }
        }
    }

    /**
     * Example 2: Edit Profile Sheet
     */
    @Composable
    fun Fragment.showEditProfileSheet() {
        showResponsiveSheet(
            bindingInflater = BottomEditProfileBinding::inflate,
            onDismiss = {
                // Handle dismiss if needed
                requireActivity().hideSoftKeyboard()
            }
        ) { binding, dialog ->
            binding.apply {
                backButton.setOnClickListener {
                    dialog.dismiss()
                }

                btnSave.setOnClickListener {
                    // Handle profile update
                    val firstName = edTxtFirstName.text.toString()
                    val lastName = edTxtLastName.text.toString()
                    val companyName = edTxtCompanyName.text.toString()
                    
                    // Validate and save
                    dialog.dismiss()
                }
            }
        }
    }

    /**
     * Example 3: Update Password Sheet
     */
    @Composable
    fun Fragment.showUpdatePasswordSheet() {
        showResponsiveSheet(BottomUpdatePasswordBinding::inflate) { binding, dialog ->
            binding.apply {
                backButton.setOnClickListener {
                    dialog.dismiss()
                }

                btnSave.setOnClickListener {
                    val currentPassword = edTxtCurrentPassword.text.toString()
                    val newPassword = edTxtNewPassword.text.toString()
                    val confirmPassword = edTxtConfirmPassword.text.toString()
                    
                    // Add validation
                    if (newPassword != confirmPassword) {
                        // Show error
                        return@setOnClickListener
                    }
                    
                    // Update password
                    dialog.dismiss()
                }
            }
        }
    }

    /**
     * Example 4: Using layout resource (backward compatibility)
     */
    @Composable
    fun Fragment.showStatusFilterSheetLegacy() {
        showResponsiveSheetWithLayout(
            layoutRes = Manaknight.R.layout.bottom_sheet_multi_select_status_filter
        ) { view, dialog ->
            // Setup RecyclerView and adapter
            val recyclerView = view.findViewById<androidx.recyclerview.widget.RecyclerView>(Manaknight.R.id.statusRecyclerView)
            // ... setup logic
        }
    }

    /**
     * Example 5: Direct usage of ResponsiveBottomSheetHelper
     */
    @Composable
    fun Fragment.showCustomSheet() {
        ResponsiveBottomSheetHelper.showResponsiveSheet(
            fragment = this,
            bindingInflater = BottomAddEmployeeBinding::inflate,
            onBindingReady = { binding, dialog ->
                // Setup views
            },
            onDismiss = {
                // Handle dismiss
            }
        )
    }
}

/**
 * Migration Guide:
 * 
 * OLD WAY:
 * ```
 * private fun showAddEmployee() {
 *     val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
 *     val view = BottomAddEmployeeBinding.inflate(layoutInflater)
 *     sheet.setContentView(view.root)
 *     // setup views...
 *     sheet.show()
 * }
 * ```
 * 
 * NEW WAY:
 * ```
 * private fun showAddEmployee() {
 *     showResponsiveSheet(BottomAddEmployeeBinding::inflate) { binding, dialog ->
 *         // setup views...
 *     }
 * }
 * ```
 * 
 * The new way automatically:
 * - Shows as bottom sheet on phones
 * - Shows as dialog on tablets
 * - Handles proper styling for both
 * - Maintains consistent behavior
 */
