package com.manaknight.app.utils

import android.app.Dialog
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.EditText
import androidx.fragment.app.DialogFragment
import androidx.viewbinding.ViewBinding
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.manaknight.app.extensions.hideSoftKeyboard
//import com.manaknight.app.ui.components.isTabletLayout
import Manaknight.R
import com.manaknight.app.extensions.isTabletLayout

/**
 * Base class for responsive dialog fragments that show as bottom sheets on phones
 * and dialogs on tablets
 */
abstract class ResponsiveBaseDialogFragment<T : ViewBinding> : DialogFragment() {

    private var _binding: T? = null
    protected val binding get() = _binding!!

    abstract fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): T

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val isTablet = isTabletLayout()
        
        return if (isTablet) {
            createTabletDialog()
        } else {
            createPhoneBottomSheet()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = getViewBinding(inflater, container)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    /**
     * Override this method to setup your views
     */
    abstract fun setupViews()

    private fun createTabletDialog(): Dialog {
        val dialog = Dialog(requireContext(), R.style.ResponsiveDialogTheme)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        
        dialog.setOnShowListener {
            dialog.window?.apply {
                setLayout(
                    resources.getDimensionPixelSize(R.dimen.tablet_dialog_width),
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                setBackgroundDrawableResource(R.drawable.dialog_background)
            }
        }
        
        return createDialogWithTouchHandling(dialog)
    }

    private fun createPhoneBottomSheet(): Dialog {
        val bottomSheetDialog = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        return createDialogWithTouchHandling(bottomSheetDialog)
    }

    private fun createDialogWithTouchHandling(dialog: Dialog): Dialog {
        return object : Dialog(dialog.context, dialog.window?.attributes?.type ?: 0) {
            override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
                if (ev.action == MotionEvent.ACTION_DOWN) {
                    val view = currentFocus
                    if (view is EditText) {
                        val outRect = Rect()
                        view.getGlobalVisibleRect(outRect)
                        if (!outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())) {
                            requireActivity().hideSoftKeyboard()
                            view.clearFocus()
                        }
                    }
                }
                return super.dispatchTouchEvent(ev)
            }
        }
    }
}
