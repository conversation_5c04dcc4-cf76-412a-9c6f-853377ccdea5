package com.manaknight.app.model.remote.profitPro;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b$\b\u0086\b\u0018\u00002\u00020\u0001Bq\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0016\u0010\u0004\u001a\u0012\u0012\u0004\u0012\u00020\u00060\u0005j\b\u0012\u0004\u0012\u00020\u0006`\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0016\u0010\u000e\u001a\u0012\u0012\u0004\u0012\u00020\u000f0\u0005j\b\u0012\u0004\u0012\u00020\u000f`\u0007\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u0012\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0002\u0010\u0015J\u0010\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001dJ\u0019\u0010)\u001a\u0012\u0012\u0004\u0012\u00020\u00060\u0005j\b\u0012\u0004\u0012\u00020\u0006`\u0007H\u00c6\u0003J\t\u0010*\u001a\u00020\tH\u00c6\u0003J\u0010\u0010+\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\t\u0010,\u001a\u00020\rH\u00c6\u0003J\u0019\u0010-\u001a\u0012\u0012\u0004\u0012\u00020\u000f0\u0005j\b\u0012\u0004\u0012\u00020\u000f`\u0007H\u00c6\u0003J\t\u0010.\u001a\u00020\u0011H\u00c6\u0003J\t\u0010/\u001a\u00020\u0013H\u00c6\u0003J\t\u00100\u001a\u00020\u0013H\u00c6\u0003J\u008c\u0001\u00101\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0018\b\u0002\u0010\u0004\u001a\u0012\u0012\u0004\u0012\u00020\u00060\u0005j\b\u0012\u0004\u0012\u00020\u0006`\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\u0018\b\u0002\u0010\u000e\u001a\u0012\u0012\u0004\u0012\u00020\u000f0\u0005j\b\u0012\u0004\u0012\u00020\u000f`\u00072\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0013H\u00c6\u0001\u00a2\u0006\u0002\u00102J\u0013\u00103\u001a\u00020\u00032\b\u00104\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00105\u001a\u00020\u000bH\u00d6\u0001J\t\u00106\u001a\u00020\u0013H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R!\u0010\u000e\u001a\u0012\u0012\u0004\u0012\u00020\u000f0\u0005j\b\u0012\u0004\u0012\u00020\u000f`\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u001e\u001a\u0004\b\u001c\u0010\u001dR!\u0010\u0004\u001a\u0012\u0012\u0004\u0012\u00020\u00060\u0005j\b\u0012\u0004\u0012\u00020\u0006`\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001bR\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0015\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\"\u0010#R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0019\u00a8\u00067"}, d2 = {"Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "", "error", "", "job_details", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "Lkotlin/collections/ArrayList;", "totals", "Lcom/manaknight/app/model/remote/profitPro/TotalRespModel;", "status", "", "client_details", "Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;", "draws", "Lcom/manaknight/app/model/remote/profitPro/DrawsRespModel;", "project", "Lcom/manaknight/app/model/remote/profitPro/ProjectRespModel;", "create_at", "", "update_at", "(Ljava/lang/Boolean;Ljava/util/ArrayList;Lcom/manaknight/app/model/remote/profitPro/TotalRespModel;Ljava/lang/Integer;Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;Ljava/util/ArrayList;Lcom/manaknight/app/model/remote/profitPro/ProjectRespModel;Ljava/lang/String;Ljava/lang/String;)V", "getClient_details", "()Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;", "getCreate_at", "()Ljava/lang/String;", "getDraws", "()Ljava/util/ArrayList;", "getError", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getJob_details", "getProject", "()Lcom/manaknight/app/model/remote/profitPro/ProjectRespModel;", "getStatus", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getTotals", "()Lcom/manaknight/app/model/remote/profitPro/TotalRespModel;", "getUpdate_at", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/Boolean;Ljava/util/ArrayList;Lcom/manaknight/app/model/remote/profitPro/TotalRespModel;Ljava/lang/Integer;Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;Ljava/util/ArrayList;Lcom/manaknight/app/model/remote/profitPro/ProjectRespModel;Ljava/lang/String;Ljava/lang/String;)Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "equals", "other", "hashCode", "toString", "app_debug"})
public final class AllLineItemsResponseModel {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean error = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> job_details = null;
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.model.remote.profitPro.TotalRespModel totals = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer status = null;
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.model.remote.profitPro.ClientDetailRespModel client_details = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> draws = null;
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.model.remote.profitPro.ProjectRespModel project = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String create_at = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String update_at = null;
    
    public AllLineItemsResponseModel(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean error, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> job_details, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.TotalRespModel totals, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ClientDetailRespModel client_details, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> draws, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ProjectRespModel project, @org.jetbrains.annotations.NotNull()
    java.lang.String create_at, @org.jetbrains.annotations.NotNull()
    java.lang.String update_at) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> getJob_details() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.TotalRespModel getTotals() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.ClientDetailRespModel getClient_details() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> getDraws() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.ProjectRespModel getProject() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCreate_at() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUpdate_at() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.TotalRespModel component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.ClientDetailRespModel component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.ProjectRespModel component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel copy(@org.jetbrains.annotations.Nullable()
    java.lang.Boolean error, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> job_details, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.TotalRespModel totals, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ClientDetailRespModel client_details, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> draws, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ProjectRespModel project, @org.jetbrains.annotations.NotNull()
    java.lang.String create_at, @org.jetbrains.annotations.NotNull()
    java.lang.String update_at) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}