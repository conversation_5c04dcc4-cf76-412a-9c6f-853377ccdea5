<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_round_corners"
    android:orientation="vertical"
    android:paddingVertical="10dp"
    app:layout_behavior="@string/bottom_sheet_behavior">


    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingTop="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="5dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                >

                <TextView
                    android:id="@+id/addHeading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="New Linear Foot Cost"
                    android:textSize="16sp"
                    android:textColor="#0A0D14"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:gravity="center" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/backButton"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerVertical="true"
                android:src="@drawable/cross" />





            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_alignParentEnd="true"
                android:paddingVertical="0dp"
                android:textColor="#375DFB"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                app:cornerRadius="8dp"
                app:strokeWidth="2dp"
                app:strokeColor="#375DFB"
                app:backgroundTint="@color/white"
                android:text="Save" />
        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#D3D3D3"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:id="@+id/mainLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude">

    <LinearLayout
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="15dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter"
            android:text="Name"
            android:textColor="#0A0D14"
            android:textFontWeight="500"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <EditText
        android:id="@+id/edTxtLinealName"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="14sp"
        android:layout_marginTop="2dp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:inputType="text"
        android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
        android:maxLines="1"
        android:padding="12dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="15dp"

        />

    <TextView
        android:id="@+id/tvFullName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="Lineal Foot cost"
        android:layout_marginHorizontal="15dp"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:textColor="#0A0D14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <LinearLayout
        android:id="@+id/linearDefaultHourlyRate2"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="2dp"
        android:padding="6dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$"
            android:layout_gravity="center"
            android:textColor="#868C98"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:paddingLeft="4dp"
            android:paddingRight="2dp"
            />

        <EditText
            android:id="@+id/edTxtLinealCost"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="3dp"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:layout_gravity="center"
            android:inputType="number"
            android:digits="1234567890."
            android:maxLines="1"
            android:background="@drawable/rounded_edittext2"
            />
    </LinearLayout>

        <TextView
            android:id="@+id/tvFullNameError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="Lineal foot cost cannot be zero."
            android:layout_marginHorizontal="15dp"
            android:textSize="12sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:textColor="#AF1D38"
            android:visibility="gone"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="Profit Overhead"
        android:layout_marginHorizontal="15dp"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:textColor="#0A0D14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/rounded_edittext_none_editable"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="2dp"
        android:padding="6dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:layout_gravity="center"
            android:textColor="#868C98"
            android:paddingLeft="4dp"
            android:paddingRight="2dp"
            />

        <EditText
            android:id="@+id/edTxtProfitOverHead"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="3dp"
            android:layout_gravity="center"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:inputType="number"
            android:digits="1234567890."
            android:enabled="false"
            android:maxLines="1"
            android:background="@drawable/rounded_edittext2_none_editable"
            />
    </LinearLayout>

    <TextView
        android:id="@+id/remaingCost"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="$210 Remaining"
        android:textSize="12sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="400"
        android:layout_marginHorizontal="15dp"
        android:textColor="#CDD0D5" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="Labor Cost"
        android:textSize="14sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        android:layout_marginHorizontal="15dp"
        android:textColor="#0A0D14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/rounded_edittext"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="2dp"
        android:padding="6dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$"
            android:layout_gravity="center"
            android:textColor="#868C98"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:paddingLeft="4dp"
            android:paddingRight="2dp"
            />

        <EditText
            android:id="@+id/edTxtLabourCost"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="3dp"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:layout_gravity="center"
            android:inputType="number"
            android:digits="1234567890."
            android:maxLines="1"
            android:background="@drawable/rounded_edittext2"
            />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="Material Cost"
        android:layout_marginHorizontal="15dp"
        android:textColor="#0A0D14"
        android:textSize="15sp"
        android:fontFamily="@font/inter"
        android:textFontWeight="500"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerInclude" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/rounded_edittext_none_editable"
        android:layout_marginHorizontal="15dp"
        android:layout_marginTop="2dp"
        android:padding="6dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearDefaultHourlyRate"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$"
            android:layout_gravity="center"
            android:textColor="#868C98"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:paddingLeft="4dp"
            android:paddingRight="2dp"
            />

        <EditText
            android:id="@+id/edTxtMaterialCost"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="3dp"
            android:layout_gravity="center"
            android:textSize="14sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:inputType="number"
            android:digits="1234567890."
            android:enabled="false"
            android:maxLines="1"
            android:background="@drawable/rounded_edittext2_none_editable"
            />
    </LinearLayout>
        <TextView
            android:id="@+id/tvMaterialCostError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="Material cost cannot be negative."
            android:layout_marginHorizontal="15dp"
            android:textSize="12sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="400"
            android:textColor="#AF1D38"
            android:visibility="gone"/>

        <CheckBox
            android:id="@+id/checkBox1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:buttonTint="@color/brand_green"
            android:checkMarkTint="@color/black"
            android:textColor="#525866"
            android:layout_marginTop="15dp"
            android:layout_marginHorizontal="15dp"
            android:textAlignment="center"
            android:textSize="14sp"
            android:checked="true"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:visibility="gone"
            android:text="   Add to default material list"/>
    </LinearLayout>
    </ScrollView>


</LinearLayout>