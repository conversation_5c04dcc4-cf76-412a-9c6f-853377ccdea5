package com.manaknight.app.utils

import android.app.Dialog
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding

/**
 * Extension functions for Fragment to easily show responsive bottom sheets
 */

/**
 * Shows a responsive sheet using ViewBinding
 * Usage: showResponsiveSheet(BottomAddEmployeeBinding::inflate) { binding, dialog ->
 *     // Setup your views here
 * }
 */
@Composable
inline fun <T : ViewBinding> Fragment.showResponsiveSheet(
    noinline bindingInflater: (LayoutInflater, ViewGroup?, Boolean) -> T,
    noinline onDismiss: (() -> Unit)? = null,
    crossinline onBindingReady: (T, Dialog) -> Unit
): Dialog {
    return ResponsiveBottomSheetHelper.showResponsiveSheet(
        fragment = this,
        bindingInflater = bindingInflater,
        onBindingReady = { binding, dialog -> onBindingReady(binding, dialog) },
        onDismiss = onDismiss
    )
}

/**
 * Shows a responsive sheet using layout resource (for backward compatibility)
 * Usage: showResponsiveSheetWithLayout(R.layout.bottom_add_employee) { view, dialog ->
 *     // Setup your views here
 * }
 */
@Composable
inline fun Fragment.showResponsiveSheetWithLayout(
    layoutRes: Int,
    noinline onDismiss: (() -> Unit)? = null,
    crossinline onViewReady: (View, Dialog) -> Unit
): Dialog {
    return ResponsiveBottomSheetHelper.showResponsiveSheetWithLayout(
        fragment = this,
        layoutRes = layoutRes,
        onViewReady = { view, dialog -> onViewReady(view, dialog) },
        onDismiss = onDismiss
    )
}

/**
 * Shows a responsive status filter sheet
 */
@Composable
fun Fragment.showResponsiveStatusFilterSheet(
    selectedStatuses: Set<String>,
    onStatusesChanged: (Set<String>) -> Unit,
    onDismiss: (() -> Unit)? = null
): Dialog {
    return showResponsiveSheetWithLayout(
        layoutRes = Manaknight.R.layout.bottom_sheet_multi_select_status_filter,
        onDismiss = onDismiss
    ) { view, dialog ->
        // Setup status filter logic here
        setupStatusFilterSheet(view, dialog, selectedStatuses, onStatusesChanged)
    }
}

/**
 * Shows a responsive month filter sheet
 */
@Composable
fun Fragment.showResponsiveMonthFilterSheet(
    selectedMonth: String,
    onMonthSelected: (String) -> Unit,
    onDismiss: (() -> Unit)? = null
): Dialog {
    return showResponsiveSheetWithLayout(
        layoutRes = Manaknight.R.layout.bottom_sheet_month_filter,
        onDismiss = onDismiss
    ) { view, dialog ->
        // Setup month filter logic here
        setupMonthFilterSheet(view, dialog, selectedMonth, onMonthSelected)
    }
}

/**
 * Helper function to setup status filter sheet
 */
private fun Fragment.setupStatusFilterSheet(
    view: View,
    dialog: Dialog,
    selectedStatuses: Set<String>,
    onStatusesChanged: (Set<String>) -> Unit
) {
    val statusOptions = listOf("All", "Draft", "Outstanding", "Active", "Completed")
    val recyclerView = view.findViewById<androidx.recyclerview.widget.RecyclerView>(Manaknight.R.id.statusRecyclerView)
    recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(requireContext())

    // Note: You'll need to import your MultiSelectStatusFilterAdapter
    // val adapter = MultiSelectStatusFilterAdapter(
    //     statusOptions = statusOptions,
    //     selectedStatuses = selectedStatuses
    // ) { status, isSelected ->
    //     val newStatuses = if (status == "All") {
    //         if (isSelected) setOf("All") else emptySet()
    //     } else {
    //         val updatedStatuses = selectedStatuses.toMutableSet()
    //         updatedStatuses.remove("All")
    //         if (isSelected) {
    //             updatedStatuses.add(status)
    //         } else {
    //             updatedStatuses.remove(status)
    //         }
    //         if (updatedStatuses.isEmpty()) {
    //             setOf("All")
    //         } else {
    //             updatedStatuses
    //         }
    //     }
    //     onStatusesChanged(newStatuses)
    //     dialog.dismiss()
    // }
    // recyclerView.adapter = adapter
}

/**
 * Helper function to setup month filter sheet
 */
private fun Fragment.setupMonthFilterSheet(
    view: View,
    dialog: Dialog,
    selectedMonth: String,
    onMonthSelected: (String) -> Unit
) {
    val monthOptions = listOf("All", "This month", "Last month", "January", "February", "March",
        "April", "May", "June", "July", "August", "September", "October", "November", "December")

    val recyclerView = view.findViewById<androidx.recyclerview.widget.RecyclerView>(Manaknight.R.id.monthRecyclerView)
    recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(requireContext())

    // Note: You'll need to import your MonthFilterAdapter
    // val adapter = MonthFilterAdapter(monthOptions) { month ->
    //     onMonthSelected(month)
    //     dialog.dismiss()
    // }
    // recyclerView.adapter = adapter
}
